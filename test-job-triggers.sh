#!/bin/bash

# Bash script to test job triggers
# Make sure your application is running on the expected port

BASE_URL="http://localhost:8080"  # Adjust port if needed
TEST_ENDPOINT="$BASE_URL/test/job-trigger"

echo "Testing Job Trigger Endpoints..."
echo "Base URL: $TEST_ENDPOINT"

# Function to make HTTP GET request and display result
test_endpoint() {
    local endpoint=$1
    local description=$2
    
    echo ""
    echo "=== Testing: $description ==="
    echo "URL: $endpoint"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$endpoint")
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo "SUCCESS: $body"
        return 0
    else
        echo "ERROR: HTTP $http_code"
        echo "Response: $body"
        return 1
    fi
}

# Test if service is available
echo ""
echo "Checking if test endpoints are available..."
if ! test_endpoint "$TEST_ENDPOINT/status" "Service Status Check"; then
    echo ""
    echo "Service is not available. Please make sure:"
    echo "1. The application is running"
    echo "2. The port is correct (default: 8080)"
    echo "3. The TestJobTriggerEndpoint is properly deployed"
    exit 1
fi

echo ""
echo "============================================================"
echo "AVAILABLE TEST ENDPOINTS:"
echo "============================================================"

# List all available endpoints
declare -a endpoints=(
    "$TEST_ENDPOINT/triggerAttachMeteraiPajakku|Trigger Attach Meterai Pajakku Job"
    "$TEST_ENDPOINT/triggerStampingOnPremPaymentReceipt|Trigger On-Premise Payment Receipt Stamping Job"
    "$TEST_ENDPOINT/triggerStampingPaymentReceipt|Trigger Payment Receipt Stamping Job"
    "$TEST_ENDPOINT/triggerSchedulerAttachMeteraiPajakku|Trigger Scheduler Attach Meterai Pajakku"
    "$TEST_ENDPOINT/retryStampingAllPaymentReceiptOnPrem|Retry All On-Premise Payment Receipt Stamping"
    "$TEST_ENDPOINT/retryStampingAllPaymentReceipt|Retry All Payment Receipt Stamping"
)

for endpoint_info in "${endpoints[@]}"; do
    IFS='|' read -r url desc <<< "$endpoint_info"
    echo "$desc:"
    echo "  $url"
done

echo ""
echo "============================================================"

# Interactive menu
while true; do
    echo ""
    echo "Select an option:"
    echo "1. Trigger Attach Meterai Pajakku Job"
    echo "2. Trigger On-Premise Payment Receipt Stamping Job"
    echo "3. Trigger Payment Receipt Stamping Job"
    echo "4. Trigger Scheduler Attach Meterai Pajakku"
    echo "5. Retry All On-Premise Payment Receipt Stamping"
    echo "6. Retry All Payment Receipt Stamping"
    echo "7. Test All Endpoints"
    echo "0. Exit"
    
    read -p "Enter your choice (0-7): " choice
    
    case $choice in
        1)
            test_endpoint "$TEST_ENDPOINT/triggerAttachMeteraiPajakku" "Trigger Attach Meterai Pajakku Job"
            ;;
        2)
            test_endpoint "$TEST_ENDPOINT/triggerStampingOnPremPaymentReceipt" "Trigger On-Premise Payment Receipt Stamping Job"
            ;;
        3)
            test_endpoint "$TEST_ENDPOINT/triggerStampingPaymentReceipt" "Trigger Payment Receipt Stamping Job"
            ;;
        4)
            test_endpoint "$TEST_ENDPOINT/triggerSchedulerAttachMeteraiPajakku" "Trigger Scheduler Attach Meterai Pajakku"
            ;;
        5)
            test_endpoint "$TEST_ENDPOINT/retryStampingAllPaymentReceiptOnPrem" "Retry All On-Premise Payment Receipt Stamping"
            ;;
        6)
            test_endpoint "$TEST_ENDPOINT/retryStampingAllPaymentReceipt" "Retry All Payment Receipt Stamping"
            ;;
        7)
            echo ""
            echo "Testing all endpoints..."
            for endpoint_info in "${endpoints[@]}"; do
                IFS='|' read -r url desc <<< "$endpoint_info"
                test_endpoint "$url" "$desc"
                sleep 1
            done
            ;;
        0)
            echo "Exiting..."
            break
            ;;
        *)
            echo "Invalid choice. Please enter 0-7."
            ;;
    esac
done

echo ""
echo "Done!"
