# PowerShell script to test job triggers
# Make sure your application is running on the expected port

$baseUrl = "http://localhost:8080"  # Adjust port if needed
$testEndpoint = "$baseUrl/test/job-trigger"

Write-Host "Testing Job Trigger Endpoints..." -ForegroundColor Green
Write-Host "Base URL: $testEndpoint" -ForegroundColor Yellow

# Function to make HTTP GET request and display result
function Test-Endpoint {
    param(
        [string]$endpoint,
        [string]$description
    )
    
    Write-Host "`n=== Testing: $description ===" -ForegroundColor Cyan
    Write-Host "URL: $endpoint" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $endpoint -Method GET -ContentType "application/json"
        Write-Host "SUCCESS: $($response.message)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "Status Code: $statusCode" -ForegroundColor Red
        }
        return $false
    }
}

# Test if service is available
Write-Host "`nChecking if test endpoints are available..." -ForegroundColor Yellow
$statusResult = Test-Endpoint "$testEndpoint/status" "Service Status Check"

if (-not $statusResult) {
    Write-Host "`nService is not available. Please make sure:" -ForegroundColor Red
    Write-Host "1. The application is running" -ForegroundColor Red
    Write-Host "2. The port is correct (default: 8080)" -ForegroundColor Red
    Write-Host "3. The TestJobTriggerEndpoint is properly deployed" -ForegroundColor Red
    exit 1
}

Write-Host "`n" + "="*60 -ForegroundColor Yellow
Write-Host "AVAILABLE TEST ENDPOINTS:" -ForegroundColor Yellow
Write-Host "="*60 -ForegroundColor Yellow

# List all available endpoints
$endpoints = @(
    @{url="$testEndpoint/triggerAttachMeteraiPajakku"; desc="Trigger Attach Meterai Pajakku Job"},
    @{url="$testEndpoint/triggerStampingOnPremPaymentReceipt"; desc="Trigger On-Premise Payment Receipt Stamping Job"},
    @{url="$testEndpoint/triggerStampingPaymentReceipt"; desc="Trigger Payment Receipt Stamping Job"},
    @{url="$testEndpoint/triggerSchedulerAttachMeteraiPajakku"; desc="Trigger Scheduler Attach Meterai Pajakku"},
    @{url="$testEndpoint/retryStampingAllPaymentReceiptOnPrem"; desc="Retry All On-Premise Payment Receipt Stamping"},
    @{url="$testEndpoint/retryStampingAllPaymentReceipt"; desc="Retry All Payment Receipt Stamping"}
)

foreach ($endpoint in $endpoints) {
    Write-Host "$($endpoint.desc):" -ForegroundColor White
    Write-Host "  $($endpoint.url)" -ForegroundColor Gray
}

Write-Host "`n" + "="*60 -ForegroundColor Yellow

# Interactive menu
do {
    Write-Host "`nSelect an option:" -ForegroundColor Yellow
    Write-Host "1. Trigger Attach Meterai Pajakku Job" -ForegroundColor White
    Write-Host "2. Trigger On-Premise Payment Receipt Stamping Job" -ForegroundColor White
    Write-Host "3. Trigger Payment Receipt Stamping Job" -ForegroundColor White
    Write-Host "4. Trigger Scheduler Attach Meterai Pajakku" -ForegroundColor White
    Write-Host "5. Retry All On-Premise Payment Receipt Stamping" -ForegroundColor White
    Write-Host "6. Retry All Payment Receipt Stamping" -ForegroundColor White
    Write-Host "7. Test All Endpoints" -ForegroundColor White
    Write-Host "0. Exit" -ForegroundColor White
    
    $choice = Read-Host "`nEnter your choice (0-7)"
    
    switch ($choice) {
        "1" { Test-Endpoint $endpoints[0].url $endpoints[0].desc }
        "2" { Test-Endpoint $endpoints[1].url $endpoints[1].desc }
        "3" { Test-Endpoint $endpoints[2].url $endpoints[2].desc }
        "4" { Test-Endpoint $endpoints[3].url $endpoints[3].desc }
        "5" { Test-Endpoint $endpoints[4].url $endpoints[4].desc }
        "6" { Test-Endpoint $endpoints[5].url $endpoints[5].desc }
        "7" { 
            Write-Host "`nTesting all endpoints..." -ForegroundColor Yellow
            foreach ($endpoint in $endpoints) {
                Test-Endpoint $endpoint.url $endpoint.desc
                Start-Sleep -Seconds 1
            }
        }
        "0" { 
            Write-Host "Exiting..." -ForegroundColor Yellow
            break 
        }
        default { 
            Write-Host "Invalid choice. Please enter 0-7." -ForegroundColor Red 
        }
    }
} while ($choice -ne "0")

Write-Host "`nDone!" -ForegroundColor Green
