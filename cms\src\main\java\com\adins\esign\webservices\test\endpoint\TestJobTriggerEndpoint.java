package com.adins.esign.webservices.test.endpoint;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.PaymentReceiptOnPremStampingLogic;
import com.adins.esign.businesslogic.api.PaymentReceiptStampingLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.job.AttachMeteraiJob;
import com.adins.esign.job.StampingOnPremPaymentReceiptJob;
import com.adins.esign.job.StampingPaymentReceiptJob;
import com.adins.esign.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/test/job-trigger")
@Api(value = "TestJobTriggerService")
@Produces({ MediaType.APPLICATION_JSON })
public class TestJobTriggerEndpoint {
    
    private static final Logger LOG = LoggerFactory.getLogger(TestJobTriggerEndpoint.class);
    
    @Autowired
    private AttachMeteraiJob attachMeteraiJob;
    
    @Autowired
    private StampingOnPremPaymentReceiptJob stampingOnPremPaymentReceiptJob;
    
    @Autowired
    private StampingPaymentReceiptJob stampingPaymentReceiptJob;
    
    @Autowired
    private SchedulerLogic schedulerLogic;
    
    @Autowired
    private PaymentReceiptOnPremStampingLogic paymentReceiptOnPremStampingLogic;
    
    @Autowired
    private PaymentReceiptStampingLogic paymentReceiptStampingLogic;
    
    @GET
    @Path("/triggerAttachMeteraiPajakku")
    public Response triggerAttachMeteraiPajakku() {
        try {
            LOG.info("Manual trigger: triggerAttachMeteraiPajakku started");
            attachMeteraiJob.runAttachMeteraiPajakku();
            LOG.info("Manual trigger: triggerAttachMeteraiPajakku completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerAttachMeteraiPajakku executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerAttachMeteraiPajakku", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    @GET
    @Path("/triggerStampingOnPremPaymentReceipt")
    public Response triggerStampingOnPremPaymentReceipt() {
        try {
            LOG.info("Manual trigger: triggerStampingOnPremPaymentReceipt started");
            stampingOnPremPaymentReceiptJob.runStampingOnPremPaymentReceipt();
            LOG.info("Manual trigger: triggerStampingOnPremPaymentReceipt completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerStampingOnPremPaymentReceipt executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerStampingOnPremPaymentReceipt", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    @GET
    @Path("/triggerStampingPaymentReceipt")
    public Response triggerStampingPaymentReceipt() {
        try {
            LOG.info("Manual trigger: triggerStampingPaymentReceipt started");
            stampingPaymentReceiptJob.runStampingPaymentReceipt();
            LOG.info("Manual trigger: triggerStampingPaymentReceipt completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerStampingPaymentReceipt executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerStampingPaymentReceipt", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    @GET
    @Path("/triggerSchedulerAttachMeteraiPajakku")
    public Response triggerSchedulerAttachMeteraiPajakku() {
        try {
            LOG.info("Manual trigger: triggerSchedulerAttachMeteraiPajakku started");
            AuditContext audit = new AuditContext("MANUAL_TEST_TRIGGER");
            schedulerLogic.attachMeteraiPajakku(audit);
            LOG.info("Manual trigger: triggerSchedulerAttachMeteraiPajakku completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerSchedulerAttachMeteraiPajakku executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerSchedulerAttachMeteraiPajakku", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    @GET
    @Path("/retryStampingAllPaymentReceiptOnPrem")
    public Response retryStampingAllPaymentReceiptOnPrem() {
        try {
            LOG.info("Manual trigger: retryStampingAllPaymentReceiptOnPrem started");
            AuditContext audit = new AuditContext("MANUAL_TEST_TRIGGER");
            paymentReceiptOnPremStampingLogic.retryStampingAllPaymentReceipt(audit);
            LOG.info("Manual trigger: retryStampingAllPaymentReceiptOnPrem completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"retryStampingAllPaymentReceiptOnPrem executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger retryStampingAllPaymentReceiptOnPrem", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    @GET
    @Path("/retryStampingAllPaymentReceipt")
    public Response retryStampingAllPaymentReceipt() {
        try {
            LOG.info("Manual trigger: retryStampingAllPaymentReceipt started");
            AuditContext audit = new AuditContext("MANUAL_TEST_TRIGGER");
            paymentReceiptStampingLogic.retryStampingAllPaymentReceipt(audit);
            LOG.info("Manual trigger: retryStampingAllPaymentReceipt completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"retryStampingAllPaymentReceipt executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger retryStampingAllPaymentReceipt", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }
    
    @GET
    @Path("/status")
    public Response getStatus() {
        return Response.ok("{\"status\":\"active\",\"message\":\"Test job trigger endpoints are available\"}").build();
    }
}
