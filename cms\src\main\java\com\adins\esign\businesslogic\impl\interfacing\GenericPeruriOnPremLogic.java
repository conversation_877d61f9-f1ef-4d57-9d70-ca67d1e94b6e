package com.adins.esign.businesslogic.impl.interfacing;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonStampingLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.FileAccessLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.interfacing.PeruriOnPremLogic;
import com.adins.esign.confins.model.DocumentToUploadBean;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.StampingDocumentType;
import com.adins.esign.constants.enums.StampingErrorDetail;
import com.adins.esign.constants.enums.StampingErrorLocation;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.AttachEmeteraiErrorDetail;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginRequestBean;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginResponseBean;
import com.adins.esign.model.custom.IncrementAgreementStampingErrorCountBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.UploadDocPajakkuResponseBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.webservices.model.DummyClientURLUploadRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.StampingEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.StampingOnPremEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.esign.webservices.model.confins.UploadStampedDocumentRequest;
import com.adins.esign.webservices.model.confins.UploadStampedDocumentResponse;
import com.adins.exceptions.EmeteraiException;
import com.adins.exceptions.EmeteraiOnPremException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.EmeteraiException.ReasonEmeterai;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

@Component
public class GenericPeruriOnPremLogic extends BaseLogic implements PeruriOnPremLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericPeruriOnPremLogic.class);
	
	@Autowired private Gson gson;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private CommonStampingLogic commonStampingLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private FileAccessLogic fileAccessLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	
	// Peruri URL
	@Value("${e-meterai.pajakku.login}") private String urlLogin;
	@Value("${emeterai.pajakku.generate.uri}") private String urlGenerate;
	@Value("${emeterai.onprem.stamp.url}") private String urlStamp;
	
	@Value("${emeterai.onprem.processnumber}") private String processNumber;
	@Value("${emeterai.pajakku.certificatelevel}") private String certifLevel;
	@Value("${emeterai.pajakku.profilename}") private String profileName;
	
	
	private EmeteraiPajakkuLoginResponseBean loginPeruri(TrDocumentH documentH, long connectTimeout, long readTimeout, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		
		try {
			String username = commonStampingLogic.getAccountUsername(documentH, audit);
			String password = commonStampingLogic.getAccountPassword(documentH, audit);
			
			if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
				LOG.error("Kontrak {}, Peruri login credential is empty", documentH.getRefNumber());
				
				String message = getMessage("businesslogic.emeterai.emptylogincredential", null, audit);
				
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, message, null, null, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.LOGIN, StampingErrorDetail.VALIDATION, false, audit);
				
				EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
				response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
				response.setMessage(message);
				return response;
			}
			
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlLogin).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
			
			// Prepare request body
			EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
			request.setUser(username);
			request.setPassword(password);
			jsonRequest = gson.toJson(request);
			
			EmeteraiPajakkuLoginRequestBean dummyRequestBean = EmeteraiPajakkuLoginRequestBean.createMaskedInstance(request);
			String logJsonRequest = gson.toJson(dummyRequestBean);
			LOG.info("Kontrak {}, Login Peruri request: {}", documentH.getRefNumber(), logJsonRequest);
			
			// Get response
			Response clientResponse = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Login Peruri response: {}", documentH.getRefNumber(), jsonResponse);
			
			EmeteraiPajakkuLoginResponseBean response = gson.fromJson(jsonResponse, EmeteraiPajakkuLoginResponseBean.class);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {			
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, jsonResponse, null, null, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.LOGIN, StampingErrorDetail.FAIL_RESPONSE, false, audit);
			}
			
			return response;
		} catch (Exception e) {
			
			LOG.error("Kontrak {}, Login Peruri exception: {}", documentH.getRefNumber(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, e.getLocalizedMessage(), e, null, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.LOGIN, StampingErrorDetail.EXCEPTION, false, audit);
			
			EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
			response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			response.setErrorMessage(e.getLocalizedMessage());
			response.setException(e);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			return response;
		}
	}
	
	private UploadDocPajakkuResponseBean uploadDocumentForStamping(TrDocumentD document, TrDocumentDStampduty docSdt, AuditContext audit) {
		try {
			// Set stamping start time
			if (null == docSdt.getStartStampProcess()) {
				docSdt.setStartStampProcess(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			}
			
			fileAccessLogic.deleteStampedDocument(document);
			
			String base64Document = commonStampingLogic.getDocumentFileToUpload(document, audit);
			byte[] fileContent = Base64.getDecoder().decode(base64Document);
			fileAccessLogic.storeBaseStampDocument(fileContent, document);
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_GEN_SDT, audit);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(GlobalVal.PERURI_SUCCESS_CODE);
			return response;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document for stamping exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DOC, StampingErrorDetail.EXCEPTION, false, audit);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMessage(response.getMessage());
			return response;
		}
	}
	
	private GenerateEmeteraiPajakkuResponse generateEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		try {
			
			// Validasi kebutuhan meterai
			int sdtNeeded = document.getTotalMaterai() - document.getTotalStamping();
			int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			LOG.info("Kontrak {}, Document {}, Need SDT: {}, Available SDT: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtNeeded, availableSdt);
			if (availableSdt == sdtNeeded) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
				
				GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
				response.setMessage("Stamp duty already available");
				response.setStatusCode(GlobalVal.PERURI_SUCCESS_CODE);
				return response;
			}
			
			//Validasi jumlah saldo meterai                                                                                                
			boolean enoughSdt = commonStampingLogic.enoughSdtBalance(document, sdtNeeded, audit);
			if (!enoughSdt) {
				String message = "Not enough balance";
				LOG.error("Kontrak {}, Dokumen {}, Generate meterai validation: Not enough balance", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.VALIDATION, false, audit);
				
				GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
				response.setMessage(message);
				response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			// Prepare API request
			String nilaiMeteraiLunas = commonStampingLogic.getStampDutyFee(audit);
			GenerateEmeteraiPajakkuRequest request = new GenerateEmeteraiPajakkuRequest();
			request.setUpload(false);
			request.setNamadoc(commonStampingLogic.getNamaDocForGenerate(document, audit));
			request.setNamafile(document.getDocumentId() + ".pdf");
			request.setNilaidoc(nilaiMeteraiLunas);
			request.setSnOnly(false);
			request.setNodoc(commonStampingLogic.getNoDocForGenerate(document, audit));
			request.setTgldoc(commonStampingLogic.getTglDocForGenerate(document, audit));
			
			if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && "1".equals(document.getTrDocumentH().getIsPostpaidStampduty())  && StringUtils.isNotBlank(document.getDocumentName())) {
				request.setNamejidentitas(document.getMsLovIdType().getCode());
				request.setNoidentitas(personalDataEncLogic.decryptToString(document.getIdNoBytea()));
				request.setNamedipungut(personalDataEncLogic.decryptToString(document.getIdNameBytea()));
			}
			
			jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, Dokumen {}, Generate meterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
			
			// Prepare API Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, "Bearer " + token);
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlGenerate).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
			
			// Post API
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			
			LOG.info("Kontrak {}, Dokumen {}, Generate meterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse);
			GenerateEmeteraiPajakkuResponse generateResponse = gson.fromJson(jsonResponse, GenerateEmeteraiPajakkuResponse.class);
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(generateResponse.getStatusCode())) {
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, jsonResponse, null, jsonRequest, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.FAIL_RESPONSE, false, audit);
				return generateResponse;
			}
			
			// Save QR to local file
			byte[] qrContent = Base64.getDecoder().decode(generateResponse.getResult().getImage());
			fileAccessLogic.storeStampQr(qrContent, generateResponse.getResult().getSn());
			
			MsTenant tenant = document.getMsTenant();
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_AVAILABLE);
			MsLov balanceType = null;
			MsLov trxType = null;
			if ("1".equals(document.getTrDocumentH().getIsPostpaidStampduty())) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID);
			} else {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
			}
			
			TrStampDuty sdt = new TrStampDuty();
			sdt.setTrxNo(trxNo);
			sdt.setStampDutyNo(generateResponse.getResult().getSn());
			sdt.setStampQr(generateResponse.getResult().getSn() + ".png");
			sdt.setMsLov(sdtStatus);
			sdt.setUsrCrt(audit.getCallerId());
			sdt.setDtmCrt(new Date());
			sdt.setMsTenant(tenant);
			sdt.setMsVendor(vendor);
			sdt.setStampDutyFee(Integer.valueOf(nilaiMeteraiLunas));
			daoFactory.getStampDutyDao().insertTrStampDutyNewTran(sdt);
			LOG.info("Kontrak {}, stamp duty with SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			docSdt.setTrStampDuty(sdt);
			docSdt.setUsrUpd(audit.getCallerId());
			docSdt.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			
			String notes = commonStampingLogic.getBalanceMutationNotesForGenerate(docSdt, audit);
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setRefNo(document.getTrDocumentH().getRefNumber());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setTrDocumentD(document);
			mutation.setTrDocumentH(document.getTrDocumentH());
			mutation.setNotes(notes);
			mutation.setTrStampDuty(sdt);
			mutation.setUsrCrt(audit.getCallerId());
			mutation.setDtmCrt(new Date());
			mutation.setMsOffice(document.getTrDocumentH().getMsOffice());
			mutation.setMsBusinessLine(document.getTrDocumentH().getMsBusinessLine());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			LOG.info("Kontrak {}, balance mutation with stamp duty SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
			return generateResponse;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Generate meterai exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e , jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.EXCEPTION, false, audit);
			
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			response.setMessage(e.getLocalizedMessage());
			response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
			
		}
	}
	
	private StampingEmeteraiPajakkuResponse stampEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		try {
			int sdtAvailable = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			LOG.info("Kontrak {}, Dokumen {}, available SDT qty: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtAvailable);
			if (0 == sdtAvailable) {
				String message = "No stamp duty for " + document.getDocumentId();
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, false, audit);
				
				StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
				response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			TrStampDuty sdt = docSdt.getTrStampDuty();
			if (GlobalVal.CODE_LOV_SDT_GO_LIVE.equals(sdt.getMsLov().getCode())) {
				String message = "SDT with number " + sdt.getStampDutyNo() + " cannot be used.";
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, false, audit);
				
				StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
				response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			SignLocationBean coordinate = gson.fromJson(docSdt.getSignLocation(), SignLocationBean.class);
			
			StampingOnPremEmeteraiPajakkuRequest request = new StampingOnPremEmeteraiPajakkuRequest();
			request.setCertificatelevel(certifLevel);
			request.setDest(commonStampingLogic.getOnPremStampDestination(document, audit));
			request.setDocpass(StringUtils.EMPTY);
			request.setJwToken(token);
			request.setLocation(document.getTrDocumentH().getMsOffice().getOfficeName());
			request.setProfileName(profileName);
			request.setReason(commonStampingLogic.getReasonForStamp(document, audit));
			request.setRefToken(sdt.getStampDutyNo());
			request.setSpesimenPath(commonStampingLogic.getOnPremSpecimenPath(docSdt, audit));
			request.setSrc(commonStampingLogic.getOnPremSource(document, audit));
			request.setRetryFlag("1");
			request.setVisLLX(Double.valueOf(coordinate.getLlx()));
			request.setVisLLY(Double.valueOf(coordinate.getLly()));
			request.setVisURX(Double.valueOf(coordinate.getUrx()));
			request.setVisURY(Double.valueOf(coordinate.getUry()));
			request.setVisSignaturePage(docSdt.getSignPage());
			
			// Prepare API Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, "Bearer " + token);
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlStamp).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
			
			// Prepare API request body
			jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, Dokumen {}, Stamping meterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
			
			// Get API response
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Dokumen {}, Stamping meterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse);
			StampingEmeteraiPajakkuResponse stampingResponse = gson.fromJson(jsonResponse, StampingEmeteraiPajakkuResponse.class);
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(stampingResponse.getErrorCode())) {
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, jsonResponse, null, jsonRequest, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.STM_SDT, StampingErrorDetail.FAIL_RESPONSE, false, audit);
				return stampingResponse;
			}
			
			// Update SDT Status to GO LIVE
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
			sdt.setMsLov(sdtStatus);
			sdt.setUsrUpd(audit.getCallerId());
			sdt.setDtmUpd(new Date());
			daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
			
			// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
			docSdt.setTrStampDuty(sdt);
			docSdt.setStampingDate(new Date());
			docSdt.setUsrUpd(audit.getCallerId());
			docSdt.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_OSS, audit);
			return stampingResponse;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Stamping meterai exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.STM_SDT, StampingErrorDetail.EXCEPTION, false, audit);
			
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMessage(e.getLocalizedMessage());
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
		}
	}
	
	private AttachEmeteraiErrorDetail storeStampedDocumentToOss(TrDocumentD document, AuditContext audit) {
		try {
			boolean isExists = false;
			int attempCount = commonStampingLogic.getFileCheckAttempts(audit);
			long delayMs = commonStampingLogic.getFileCheckDelay(audit);
		
			LOG.info("Kontrak {}, Dokumen {}, Upload to OSS", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
			while (!isExists) {
				if (attempCount <= 0) {
					LOG.error("Kontrak {}, Dokumen {}, Max check count reached", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
					throw new EmeteraiOnPremException(getMessage("businesslogic.emeterai.onprem.filecheckfailed", null, audit));
				}
				
				Thread.sleep(delayMs);
				LOG.info("Kontrak {}, Dokumen {}, {} attempts left to check stamped document", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), attempCount);
				isExists = fileAccessLogic.isStampedDocumentExists(document);
				attempCount -= 1;
			}
				
			byte[] documentByteArray = fileAccessLogic.getStampedDocument(document);
			if (null == documentByteArray) {
				throw new EmeteraiOnPremException(getMessage("businesslogic.emeterai.onprem.filecheckfailed", null, audit));
			}
			
			cloudStorageLogic.storeStampedDocument(document, documentByteArray);
			
			// Update documentD
			short stamped = document.getTotalStamping();
			stamped += 1;
			document.setTotalStamping(stamped);
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
						
			if (document.getTotalMaterai().equals(document.getTotalStamping())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_CON, audit);
			} else {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
			}
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(GlobalVal.PERURI_SUCCESS_CODE);
			return response;
		} catch (InterruptedException e) {
			
			LOG.error("Kontrak {}, Dokumen {}, Upload to OSS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			LOG.info("Kontrak {}, Dokumen {}, interrupting current thread", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
			
			Thread.currentThread().interrupt();
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_OSS, StampingErrorDetail.EXCEPTION, false, audit);
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload to OSS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_OSS, StampingErrorDetail.EXCEPTION, false, audit);
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		
	}
	
	private UploadToCoreBean prepareUploadToCoreSingleDocument(TrDocumentD document, AuditContext audit) {
		
		LOG.info("Kontrak {}, Dokumen {}, preparing upload to DMS", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
		
		List<DocumentToUploadBean> documents = new ArrayList<>();
		UploadToCoreBean bean = new UploadToCoreBean();
		DocumentToUploadBean documentBean = new DocumentToUploadBean();
		
		documentBean.setDocTypeTc(document.getMsDocTemplate().getDocTemplateCode());
		documentBean.setDisplayName(document.getMsDocTemplate().getDocTemplateName());
		
		String base64Document = commonStampingLogic.getStampedDocumentFromOss(document, audit);
		documentBean.setContent(base64Document);
		documentBean.setFileName(GlobalVal.PREFIX_DOCUMENT_FILE_NAME + document.getDocumentId() + ".pdf");
		documents.add(documentBean);
		
		bean.setDocumentObjs(documents);
		bean.setRefNo(document.getTrDocumentH().getRefNumber());
		return bean;
	}
	
	private Status uploadStampedDocumentToWomfDms(TrDocumentD document, AuditContext audit) {
		try {
			UploadToCoreBean bean = prepareUploadToCoreSingleDocument(document, audit);
			Status status = documentLogic.callUrlUpload(document.getTrDocumentH().getUrlUpload(), bean);
			if (200 != status.getCode()) {
				IncrementAgreementStampingErrorCountBean errorBean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, status.getMessage(), null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(errorBean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.FAIL_RESPONSE, false, audit);
			}
			return status;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to DMS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.EXCEPTION, false, audit);
			
			Status status = new Status();
			status.setCode(StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION);
			status.setMessage(e.getLocalizedMessage());
			return status;
		}
	}
	
	private Status uploadStampedDocumentToCfiDms(TrDocumentD document, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		try {
			String url = document.getTrDocumentH().getUrlUpload();
			if (StringUtils.isEmpty(url)) {
				throw new EmeteraiException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
						new String[] {"Upload URL"}, audit), ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			header.put("Integration", commonStampingLogic.getIntegrationValue(document, audit));
			Headers headers = Headers.of(header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS request header: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS with URL: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), url);
			
			// Prepare json request body
			String documentDate = MssTool.formatDateToStringIn(document.getRequestDate(), "yyyy/MM/dd");
			
			UploadStampedDocumentRequest request = new UploadStampedDocumentRequest();
			request.setDokumenPeruri(document.getMsPeruriDocType().getDocName());
			request.setDokumenDate(documentDate);
			request.setFilename(document.getDocumentName() + ".pdf");
			request.setContent(commonStampingLogic.getStampedDocumentFromOss(document, audit));
			request.setNotes(document.getTrDocumentH().getMsLov().getDescription());
			request.setDocumentId(document.getDocumentId());
			
			UploadStampedDocumentRequest logRequest = UploadStampedDocumentRequest.newInstance(request);
			logRequest.setContent("base64doc");
			String logJson = gson.toJson(logRequest);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS request body: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), logJson);
			
			String jsonBody = gson.toJson(request);
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			jsonResponse = result;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			UploadStampedDocumentResponse uploadResponse = gson.fromJson(result, UploadStampedDocumentResponse.class);
			
			if (!"200".equals(uploadResponse.getStatusCode())) {
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, jsonResponse, null, jsonRequest, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.FAIL_RESPONSE, false, audit);
			}
			
			Status status = new Status();
			status.setCode(Integer.valueOf(uploadResponse.getStatusCode()));
			status.setMessage(uploadResponse.getMessage());
			return status;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to DMS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.EXCEPTION, false, audit);
			
			Status status = new Status();
			status.setCode(StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION);
			status.setMessage(e.getLocalizedMessage());
			return status;
		}
	}
	
	private Status uploadDStampedDocumentToClient(TrDocumentD document, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		try {
			String url = document.getTrDocumentH().getUrlUpload();
			if (StringUtils.isEmpty(url)) {
				throw new EmeteraiException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
						new String[] {"Upload URL"}, audit), ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			String token = daoFactory.getTenantSettingsDao().getTenantSettings(document.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD).getSettingValue();
			commonValidatorLogic.validateNotNull(token, getMessage("businesslogic.tenantsettings.tenantsettingsnotfoundfortenant", new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD, document.getMsTenant().getTenantName()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			header.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
			header.put("token", token);
			Headers headers = Headers.of(header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client request header: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client with URL: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), url);
			
			// Prepare json request body
			AuditDataType auditDataType = new AuditDataType();
			auditDataType.setCallerId(audit.getCallerId());
			
			String base64Document = commonStampingLogic.getStampedDocumentFromOss(document, audit);
			DummyClientURLUploadRequest request = new DummyClientURLUploadRequest();
			request.setDocFile(base64Document);
			request.setDocNumber(document.getTrDocumentH().getRefNumber());
			request.setTenantCode(document.getMsTenant().getTenantCode());
			request.setAudit(auditDataType);
			String jsonBody = gson.toJson(request);
			
			// Logging json request
			DummyClientURLUploadRequest loggingRequest = DummyClientURLUploadRequest.newInstance(request);
			loggingRequest.setDocFile("base64doc");
			String logJson = gson.toJson(loggingRequest);
			jsonRequest = logJson;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client request body: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), logJson);
			
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			jsonResponse = result;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Client response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			MssResponseType uploadResponse = gson.fromJson(result, MssResponseType.class);
			
			if (200 != uploadResponse.getStatus().getCode()) {
				IncrementAgreementStampingErrorCountBean errorBean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, uploadResponse.getStatus().getMessage(), null, logJson, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(errorBean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.FAIL_RESPONSE, false, audit);
			}
			
			Status status = new Status();
			status.setCode(uploadResponse.getStatus().getCode());
			status.setMessage(uploadResponse.getStatus().getMessage());
			return status;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to client exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.EXCEPTION, false, audit);
			
			Status status = new Status();
			status.setCode(StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION);
			status.setMessage(e.getLocalizedMessage());
			return status;
		} 
	}
	
	private Status uploadStampedDocument(TrDocumentD document, AuditContext audit) {
		
		// Kalau URL upload kosong, anggap sukses dengan return status code 200
		if (StringUtils.isBlank(document.getTrDocumentH().getUrlUpload())) {
			Status status = new Status();
			status.setCode(200);
			return status;
		}
		
		// Upload stamped document ke client dengan standard request format
		if ("1".equals(document.getTrDocumentH().getIsStandardUploadUrl())) {
			return uploadDStampedDocumentToClient(document, audit);
		}
		
		// Upload DMS untuk flow biasa WOMF (TTD -> Stamp Meterai)
		if (GlobalVal.TENANT_CODE_WOMF.equals(document.getMsTenant().getTenantCode())
				&& !"1".equals(document.getTrDocumentH().getIsManualUpload())) {	
			return uploadStampedDocumentToWomfDms(document, audit);
		}
		
		// Upload DMS untuk emeterai CFI (Upload manual dari menu embed)
		if (GlobalVal.TENANT_CODE_CFI.equals(document.getMsTenant().getTenantCode())
				&& "1".equals(document.getTrDocumentH().getIsManualUpload())) {
			return uploadStampedDocumentToCfiDms(document, audit);
		}
		
		String message = "Uhandled data in upload DMS";
		LOG.warn("Kontrak {}, Dokumen {}, Uhandled data in upload DMS", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
		IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
		commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.UPL_DMS, StampingErrorDetail.VALIDATION, false, audit);
		
		Status status = new Status();
		status.setCode(StatusCode.PAJAKKU_ERROR);
		status.setMessage(message);
		return status;	
	}

	@Override
	public UpdateStampDutyStatusResponse attachMeteraiPeruri(AuditContext audit) {
		Short meteraiProcess = Short.valueOf(processNumber);
		List<TrDocumentH> documentHs = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(meteraiProcess);
		for (int i = 0; i < documentHs.size(); i++) {
			attachContractWithMeterai(documentHs.get(i), audit);
			LOG.info("Job On-Premise stamping, done processing {} out of {} contract(s)", i+1, documentHs.size());
		}
		return new UpdateStampDutyStatusResponse();
	}
	
	private UpdateStampDutyStatusResponse attachContractWithMeterai(TrDocumentH documentH, AuditContext audit) {
		
		commonStampingLogic.updateDocumentHMeteraiProcess(documentH, GlobalVal.ON_PREM_STAMP_IN_PROGRESS, audit);
		
		long connectTimeout = commonStampingLogic.getStampingConnectionTimeout(StampingDocumentType.REGULAR_DOCUMENT, audit);
		long readTimeout = commonStampingLogic.getStampingReadTimeout(StampingDocumentType.REGULAR_DOCUMENT, audit);
		
		EmeteraiPajakkuLoginResponseBean loginResponse = loginPeruri(documentH, connectTimeout, readTimeout, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
			return new UpdateStampDutyStatusResponse();
		}
		
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		for (TrDocumentD document : documents) {
			LOG.info("Kontrak {}, Dokumen {}, Total Meterai: {}, Total Stamping: {}", documentH.getRefNumber(), document.getDocumentId(), document.getTotalMaterai(), document.getTotalStamping());
			
			if (null == document.getTotalMaterai() || 0 == document.getTotalMaterai()) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_SDT_FIN, audit);
				continue;
			}
			
			List<TrDocumentDStampduty> docSdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(document.getIdDocumentD());
			String loginToken = loginResponse.getToken();
			int currentLoop = document.getTotalStamping();
			for (int i = currentLoop; i < document.getTotalMaterai(); i++) {
				TrDocumentDStampduty docSdt = docSdts.get(i);
				if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(document.getSdtProcess())) {
					commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
				}
				if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(document.getSdtProcess())) {
					UploadDocPajakkuResponseBean response = uploadDocumentForStamping(document, docSdt, audit);
					if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
						return new UpdateStampDutyStatusResponse();
					}
				}
				if (GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
					GenerateEmeteraiPajakkuResponse response = generateEmeterai(document, docSdt, loginToken, connectTimeout, readTimeout, audit);
					if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
						return new UpdateStampDutyStatusResponse();
					}
				}
				if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
					StampingEmeteraiPajakkuResponse response = stampEmeterai(document, docSdt, loginToken, connectTimeout, readTimeout, audit);
					if (!GlobalVal.PERURI_STAMPING_SUCCESS_STATUS.equalsIgnoreCase(response.getStatus())) {
						return new UpdateStampDutyStatusResponse();
					}
				}
				if (GlobalVal.STEP_ATTACH_METERAI_UPL_OSS.equals(document.getSdtProcess())) {
					AttachEmeteraiErrorDetail response = storeStampedDocumentToOss(document, audit);
					if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
						return new UpdateStampDutyStatusResponse();
					}
				}
				LOG.info("Kontrak {}, Dokumen {}, Total stamped: {}/{}", documentH.getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai());
			}
			
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_CON.equals(document.getSdtProcess())) {
				Status response = uploadStampedDocument(document, audit);
				if (200 != response.getCode()) {
					return new UpdateStampDutyStatusResponse();
				}
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_SDT_FIN, audit);
			}
		}
		
		if (commonStampingLogic.allDocumentsProcessed(documents)) {
			documentH.setCallbackProcess((short) 901);
			documentH.setProsesMaterai(new Short(GlobalVal.ON_PREM_STAMP_SUCCESS));
			documentH.setDtmUpd(new Date());
			documentH.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			LOG.info("Kontrak {} update Proses Meterai to {}", documentH.getRefNumber(), GlobalVal.ON_PREM_STAMP_SUCCESS);
			return new UpdateStampDutyStatusResponse();
		}
		
		String message = "Should not reach this process. Please check the data";
		IncrementAgreementStampingErrorCountBean errorBean = new IncrementAgreementStampingErrorCountBean(documentH, null, message, null, null, null);
		commonStampingLogic.incrementAgreementStampingErrorCount(errorBean, processNumber, StampingDocumentType.REGULAR_DOCUMENT, StampingErrorLocation.FINAL_VAL, StampingErrorDetail.VALIDATION, false, audit);
		return new UpdateStampDutyStatusResponse();
	}

}
