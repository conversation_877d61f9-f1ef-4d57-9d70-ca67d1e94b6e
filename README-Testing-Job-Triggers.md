# Testing Job Triggers for Decryption Logic

This guide helps you test the job triggers `triggerAttachMeteraiPajakku()`, `triggerStampingOnPremPaymentReceipt()`, and `triggerStampingPaymentReceipt()` to verify that the decryption logic works correctly:

```java
request.setNoidentitas(personalDataEncLogic.decryptToString(document.getIdNoBytea()));
request.setNamedipungut(personalDataEncLogic.decryptToString(document.getIdNameBytea()));
```

## Files Created

1. **TestJobTriggerEndpoint.java** - REST endpoints to manually trigger jobs
2. **test-job-triggers.ps1** - PowerShell script for Windows testing
3. **test-job-triggers.sh** - Bash script for Linux/Mac testing
4. **test-data-setup.sql** - SQL queries to check and prepare test data

## Prerequisites

### 1. Application Setup
- Ensure your eSignHub application is running
- Default port is 8080 (adjust scripts if different)
- The TestJobTriggerEndpoint should be automatically deployed

### 2. Data Requirements
The decryption logic is triggered when documents meet these conditions:

```java
if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && 
    "1".equals(document.getTrDocumentH().getIsPostpaidStampduty()) && 
    StringUtils.isNotBlank(document.getDocumentName())) {
    // Decryption logic executes here
}
```

**Required conditions:**
- `is_manual_upload = '1'`
- `is_postpaid_stampduty = '1'`
- `document_name` is not blank
- `id_no_bytea` and `id_name_bytea` contain encrypted data
- `proses_meterai` is set to appropriate process number (52 for Pajakku, 522 for On-Premise Payment Receipt)

## Testing Steps

### Step 1: Check Test Data
Run the SQL queries in `test-data-setup.sql` to verify you have documents that meet the criteria:

```sql
-- Check existing documents ready for processing
SELECT 
    dh.id_document_h,
    dh.ref_number,
    dh.is_manual_upload,
    dh.is_postpaid_stampduty,
    dd.document_name,
    CASE WHEN dd.id_no_bytea IS NOT NULL THEN 'HAS_ID_NO' ELSE 'NO_ID_NO' END as id_no_status,
    CASE WHEN dd.id_name_bytea IS NOT NULL THEN 'HAS_ID_NAME' ELSE 'NO_ID_NAME' END as id_name_status
FROM tr_document_h dh
JOIN tr_document_d dd ON dh.id_document_h = dd.id_document_h
WHERE dh.proses_meterai = 52  -- For Pajakku
   OR dh.proses_meterai = 522  -- For On-Premise Payment Receipt
ORDER BY dh.dtm_crt DESC;
```

### Step 2: Test the Endpoints

#### Option A: Using PowerShell (Windows)
```powershell
.\test-job-triggers.ps1
```

#### Option B: Using Bash (Linux/Mac)
```bash
chmod +x test-job-triggers.sh
./test-job-triggers.sh
```

#### Option C: Manual cURL Commands
```bash
# Test service availability
curl -X GET "http://localhost:8080/test/job-trigger/status"

# Trigger Attach Meterai Pajakku
curl -X GET "http://localhost:8080/test/job-trigger/triggerAttachMeteraiPajakku"

# Trigger On-Premise Payment Receipt Stamping
curl -X GET "http://localhost:8080/test/job-trigger/triggerStampingOnPremPaymentReceipt"

# Trigger Payment Receipt Stamping
curl -X GET "http://localhost:8080/test/job-trigger/triggerStampingPaymentReceipt"
```

## Available Endpoints

| Endpoint | Description | Job Method |
|----------|-------------|------------|
| `/test/job-trigger/status` | Check if endpoints are available | - |
| `/test/job-trigger/triggerAttachMeteraiPajakku` | Trigger Pajakku meterai attachment | `AttachMeteraiJob.runAttachMeteraiPajakku()` |
| `/test/job-trigger/triggerStampingOnPremPaymentReceipt` | Trigger on-premise payment receipt stamping | `StampingOnPremPaymentReceiptJob.runStampingOnPremPaymentReceipt()` |
| `/test/job-trigger/triggerStampingPaymentReceipt` | Trigger payment receipt stamping | `StampingPaymentReceiptJob.runStampingPaymentReceipt()` |
| `/test/job-trigger/triggerSchedulerAttachMeteraiPajakku` | Trigger scheduler meterai attachment | `SchedulerLogic.attachMeteraiPajakku()` |
| `/test/job-trigger/retryStampingAllPaymentReceiptOnPrem` | Retry all on-premise payment receipt stamping | `PaymentReceiptOnPremStampingLogic.retryStampingAllPaymentReceipt()` |
| `/test/job-trigger/retryStampingAllPaymentReceipt` | Retry all payment receipt stamping | `PaymentReceiptStampingLogic.retryStampingAllPaymentReceipt()` |

## Monitoring the Decryption Logic

### 1. Check Application Logs
Monitor your application logs for:
- Job execution messages
- Decryption operations
- Any errors during processing

### 2. Key Log Messages to Look For
```
Job attach meterai [process_number] Pajakku started
Attach meterai Pajakku job: Invoking FC to attach meterai Pajakku for tenant: [tenant], ref number: [ref]
Job On-Premise payment receipt stamping started
Job Stamping Payment Receipt Started
```

### 3. Database Monitoring
Check the `proses_meterai` field changes:
- Process 52 → 54 (Pajakku processing)
- Process 522 → 524 (On-Premise Payment Receipt processing)

## Troubleshooting

### Common Issues

1. **Endpoints Not Available (404)**
   - Ensure TestJobTriggerEndpoint.java is compiled and deployed
   - Check if the application is running on the correct port
   - Verify the context path configuration

2. **No Documents Processed**
   - Check if documents meet the required conditions
   - Verify `proses_meterai` values (52 for Pajakku, 522 for On-Premise)
   - Ensure `id_no_bytea` and `id_name_bytea` contain encrypted data

3. **Decryption Errors**
   - Check encryption key configuration
   - Verify PersonalDataEncryptionLogic is properly configured
   - Check if the encrypted data format is correct

4. **Job Configuration Issues**
   - Verify job properties in application.properties
   - Check if required beans are properly autowired
   - Ensure database connections are working

### Debug Steps

1. **Enable Debug Logging**
   Add to your logback configuration:
   ```xml
   <logger name="com.adins.esign.job" level="DEBUG"/>
   <logger name="com.adins.esign.businesslogic.impl.interfacing" level="DEBUG"/>
   ```

2. **Check Database State Before/After**
   ```sql
   -- Before triggering
   SELECT proses_meterai, dtm_upd FROM tr_document_h WHERE id_document_h = [your_test_id];
   
   -- After triggering
   SELECT proses_meterai, dtm_upd FROM tr_document_h WHERE id_document_h = [your_test_id];
   ```

3. **Verify Encryption/Decryption**
   Test the PersonalDataEncryptionLogic separately to ensure it works correctly.

## Expected Results

When the jobs run successfully and find documents meeting the criteria, you should see:

1. **Log Messages** indicating job execution
2. **Database Updates** showing `proses_meterai` status changes
3. **Successful Decryption** of `id_no_bytea` and `id_name_bytea` fields
4. **API Calls** to external services (Pajakku, etc.) with decrypted data

The decryption logic will execute and populate the request fields:
- `request.setNoidentitas()` with decrypted ID number
- `request.setNamedipungut()` with decrypted ID name

## Security Note

This testing endpoint is for development/testing purposes only. Do not deploy to production environments without proper security measures.
