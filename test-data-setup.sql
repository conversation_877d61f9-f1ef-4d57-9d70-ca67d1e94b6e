-- SQL script to set up test data for testing the decryption logic
-- This script helps create documents with encrypted idNoBytea and idNameBytea fields

-- Note: You'll need to adjust the values based on your actual database schema and encryption logic

-- 1. First, check if there are existing documents that meet the criteria
SELECT 
    dh.id_document_h,
    dh.ref_number,
    dh.is_manual_upload,
    dh.is_postpaid_stampduty,
    dd.document_name,
    dd.id_no_bytea,
    dd.id_name_bytea,
    dd.proses_meterai
FROM tr_document_h dh
JOIN tr_document_d dd ON dh.id_document_h = dd.id_document_h
WHERE dh.is_manual_upload = '1' 
  AND dh.is_postpaid_stampduty = '1'
  AND dd.document_name IS NOT NULL
  AND dd.id_no_bytea IS NOT NULL
  AND dd.id_name_bytea IS NOT NULL
  AND dh.proses_meterai IN (52, 522)  -- Pajakku process numbers
ORDER BY dh.dtm_crt DESC
LIMIT 10;

-- 2. Check documents ready for Pajakku stamping (process 52)
SELECT 
    dh.id_document_h,
    dh.ref_number,
    dh.proses_meterai,
    dh.is_manual_upload,
    dh.is_postpaid_stampduty,
    dd.document_name,
    CASE WHEN dd.id_no_bytea IS NOT NULL THEN 'HAS_ID_NO' ELSE 'NO_ID_NO' END as id_no_status,
    CASE WHEN dd.id_name_bytea IS NOT NULL THEN 'HAS_ID_NAME' ELSE 'NO_ID_NAME' END as id_name_status
FROM tr_document_h dh
JOIN tr_document_d dd ON dh.id_document_h = dd.id_document_h
WHERE dh.proses_meterai = 52  -- Ready for Pajakku processing
ORDER BY dh.dtm_crt DESC
LIMIT 10;

-- 3. Check documents ready for On-Premise Payment Receipt stamping (process 522)
SELECT 
    dh.id_document_h,
    dh.ref_number,
    dh.proses_meterai,
    dh.is_manual_upload,
    dh.is_postpaid_stampduty,
    dd.document_name,
    CASE WHEN dd.id_no_bytea IS NOT NULL THEN 'HAS_ID_NO' ELSE 'NO_ID_NO' END as id_no_status,
    CASE WHEN dd.id_name_bytea IS NOT NULL THEN 'HAS_ID_NAME' ELSE 'NO_ID_NAME' END as id_name_status
FROM tr_document_h dh
JOIN tr_document_d dd ON dh.id_document_h = dd.id_document_h
WHERE dh.proses_meterai = 522  -- Ready for On-Premise Payment Receipt processing
ORDER BY dh.dtm_crt DESC
LIMIT 10;

-- 4. If you need to create test data, here's a template (adjust values as needed):
/*
-- Example: Update an existing document to have the required conditions
UPDATE tr_document_h 
SET 
    is_manual_upload = '1',
    is_postpaid_stampduty = '1',
    proses_meterai = 52  -- Set to 52 for Pajakku processing
WHERE ref_number = 'YOUR_TEST_REF_NUMBER';

-- Example: Update document detail with encrypted test data
-- Note: You'll need to use your actual encryption logic to generate these byte arrays
UPDATE tr_document_d 
SET 
    document_name = 'Test Document',
    id_no_bytea = decode('YOUR_ENCRYPTED_ID_NO_HEX', 'hex'),  -- Replace with actual encrypted data
    id_name_bytea = decode('YOUR_ENCRYPTED_ID_NAME_HEX', 'hex')  -- Replace with actual encrypted data
WHERE id_document_h = (SELECT id_document_h FROM tr_document_h WHERE ref_number = 'YOUR_TEST_REF_NUMBER');
*/

-- 5. Check the LOV values for ID types
SELECT 
    lov_code,
    lov_desc,
    lov_group
FROM ms_lov 
WHERE lov_group = 'ID_TYPE'
ORDER BY lov_code;

-- 6. Check available document templates
SELECT 
    doc_template_code,
    doc_template_name,
    is_active
FROM ms_doc_template 
WHERE is_active = '1'
ORDER BY doc_template_name
LIMIT 10;

-- 7. Check tenant configuration
SELECT 
    tenant_code,
    tenant_name,
    is_active
FROM ms_tenant 
WHERE is_active = '1'
ORDER BY tenant_code
LIMIT 10;

-- 8. Check vendor configuration
SELECT 
    vendor_code,
    vendor_name,
    is_active
FROM ms_vendor 
WHERE is_active = '1'
ORDER BY vendor_code;

-- 9. Check general settings related to Pajakku
SELECT 
    gs_code,
    gs_value,
    gs_desc
FROM ms_general_setting 
WHERE gs_code LIKE '%pajakku%' OR gs_code LIKE '%meterai%'
ORDER BY gs_code;

-- 10. Check stamp duty process status
SELECT DISTINCT
    sdt_process,
    COUNT(*) as count
FROM tr_document_d 
WHERE sdt_process IS NOT NULL
GROUP BY sdt_process
ORDER BY sdt_process;
